package com.vbs.capsAllocation.repository;

import com.vbs.capsAllocation.model.Employee;
import com.vbs.capsAllocation.model.LeaveUsageLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface LeaveUsageLogRepository extends JpaRepository<LeaveUsageLog,Long> {

    // Sum total WFH used by an employee
    @Query("SELECT COALESCE(SUM(l.daysTaken), 0) FROM LeaveUsageLog l WHERE l.employee.ldap = :ldap AND l.leaveType = :leaveType")
    double sumDaysTakenByLdapAndLeaveType(@Param("ldap") String ldap, @Param("leaveType") String leaveType);

    // Sum total WFH used by an employee in a particular quarter and year
    @Query("SELECT COALESCE(SUM(l.daysTaken), 0) FROM LeaveUsageLog l WHERE l.employee.ldap = :ldap AND l.leaveType = :leaveType AND l.quarter = :quarter AND l.year = :year")
    double sumDaysTakenByLdapAndLeaveTypeAndQuarter(@Param("ldap") String ldap,
                                                    @Param("leaveType") String leaveType,
                                                    @Param("quarter") String quarter,
                                                    @Param("year") String year);

    List<LeaveUsageLog> findByEmployeeAndLeaveDateBetweenAndYear(Employee employee, LocalDate fromDate, LocalDate toDate, Integer year);
}
