# Teamsphere - Capacity Allocation System

## Project Overview
Teamsphere is a full-stack application for managing employee capacity allocation, timesheets, and project assignments. It consists of:
- **Backend**: Java Spring Boot application (capsAllocation)
- **Frontend**: Angular application (org-chart)

## Technologies Used

### Backend
- Java 17
- Spring Boot 3.4.0
- Spring Data JPA
- Spring Security
- JWT Authentication
- PostgreSQL
- Google Sheets API
- Jackson JSON
- Lombok
- Spring Mail

### Frontend
- Angular 16
- Angular Material
- Highcharts
- GoJS (for org charts)
- FileSaver.js
- XLSX (Excel export)

## Setup Instructions

### Prerequisites
- Java 17 JDK
- Node.js 18+
- PostgreSQL 14+
- Maven

### Backend Setup
1. Create PostgreSQL database:
```bash
createdb vbs_allocation_caps
```

2. Navigate to backend:
```bash
cd backend/capsAllocation
```

3. Update database credentials in [`application.properties`](backend/capsAllocation/src/main/resources/application.properties:1-3):
```properties
spring.datasource.url=****************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

4. Build and run:
```bash
./mvnw spring-boot:run
```

### Frontend Setup
1. Navigate to frontend:
```bash
cd org-chart
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm start
```

## Configuration

### Key Configuration Files
- [`application.properties`](backend/capsAllocation/src/main/resources/application.properties) - Backend configuration
- [`environment.ts`](org-chart/src/environments/environment.ts) - Frontend environment

### Environment Variables
| Variable | Description | Default |
|----------|-------------|---------|
| `SPRING_DATASOURCE_URL` | Database URL | `****************************************************` |
| `SPRING_DATASOURCE_USERNAME` | Database username | `postgres` |
| `SPRING_DATASOURCE_PASSWORD` | Database password | - |
| `SPRING_MAIL_USERNAME` | Email service username | - |
| `SPRING_MAIL_PASSWORD` | Email service password | - |

## Deployment
Use the provided deployment scripts:
- [`deploy.sh`](deploy.sh) - Main deployment script
- [`deploy-to-vm.sh`](deploy-to-vm.sh) - Deployment to virtual machine
- [`create-installer-package.sh`](create-installer-package.sh) - Create installation package

## Project Structure

### Backend
```
backend/capsAllocation/
├── src/main/java
│   ├── config/          # Configuration classes
│   ├── controller/      # REST controllers
│   ├── dto/             # Data Transfer Objects
│   ├── model/           # Entity classes
│   ├── repository/      # JPA repositories
│   ├── service/         # Business logic
│   └── util/            # Utility classes
├── src/main/resources
│   ├── db/migration/    # Database migration scripts
│   └── templates/       # Email templates
```

### Frontend
```
org-chart/
├── src/app/
│   ├── approval-table/  # Approval management
│   ├── attendance-module/ # Attendance tracking
│   ├── time-entry/      # Timesheet management
│   ├── shared/          # Shared components and services
│   └── ...              # Other feature modules
```

## License
Proprietary - © VBS Software Solutions